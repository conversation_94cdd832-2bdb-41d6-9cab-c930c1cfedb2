import { Request, Response } from "express";
import ExcelJS from "exceljs";
import { format } from "@fast-csv/format";
import stream from "stream";
import formSubmissionRepository from "../repositories/formSubmissionRepository";
import exportFileRepository from "../repositories/exportFileRepository";

interface UserRequest extends Request {
  user?: {
    id: number;
  };
}

// 📦 Generate and store Excel or CSV

export const generateAndSaveExport = async (
  req: UserRequest,
  res: Response
) => {
  try {
    const { type } = req.query; // "excel" or "csv"
    const projectId = Number(req.params.id);
    // XXXXXXXXXX this is used in multiple places [ unsafe without prior null check]
    const userId = Number(req.user!.id);
    const formSubmissions = await formSubmissionRepository.findByProjectId(
      projectId
    );

    if (!["excel", "csv"].includes(String(type))) {
      return res.status(400).json({ message: "Invalid file type" });
    }

    // Step 1: Get all unique question labels
    const questionSet = new Set<string>();
    formSubmissions.forEach((form) => {
      form.answers.forEach((answer) => {
        if (answer.question?.label) {
          questionSet.add(answer.question.label);
        }
      });
    });
    const dynamicQuestions = Array.from(questionSet);

    // Step 2: Define fixed columns
    const fixedHeaders = [
      "ID",
      "Submission Time",
      "Submitted By",
      "Device Info",
      "Location",
      "Is Other Option",
    ];

    // Combine fixed + dynamic headers
    const allHeaders = [...fixedHeaders, ...dynamicQuestions];

    let buffer: Buffer;
    let contentType: string;
    let fileName: string;

    if (type === "excel") {
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("Answers");

      // Define columns in Excel
      worksheet.columns = allHeaders.map((header) => ({
        header,
        key: header,
        width: 30,
      }));

      // Add each form submission row
      formSubmissions.forEach((form) => {
        const row: any = {
          ID: form.id,
          "Submission Time": form.submittedAt?.toISOString() || "N/A",
          "Submitted By": (form as any).user?.name || "N/A",
          "Device Info": form.deviceInfo || "N/A",
          Location: form.location || "N/A",
          "Is Other Option": form.answers.some((a) => a.isOtherOption) || false,
        };

        // Map answers by question label
        form.answers.forEach((answer) => {
          const label = answer.question?.label;
          if (label) {
            row[label] = answer.value;
          }
        });

        worksheet.addRow(row);
      });

      const buf = await workbook.xlsx.writeBuffer();
      buffer = Buffer.from(buf);
      contentType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      fileName = `answers-${projectId}.xlsx`;
    } else {
      // CSV generation
      const csvBufferStream = new stream.PassThrough();
      const chunks: any[] = [];

      const csvStream = format({ headers: allHeaders });
      csvStream.pipe(csvBufferStream);

      for (const form of formSubmissions) {
        const row: any = {
          ID: form.id,
          "Submission Time": form.submittedAt?.toISOString() || "N/A",
          "Submitted By": (form as any).user?.name || "N/A",
          "Device Info": form.deviceInfo || "N/A",
          Location: form.location || "N/A",
          "Is Other Option": form.answers.some((a) => a.isOtherOption) || false,
        };

        form.answers.forEach((answer) => {
          const label = answer.question?.label;
          if (label) {
            row[label] = answer.value;
          }
        });

        csvStream.write(row);
      }

      csvStream.end();

      for await (const chunk of csvBufferStream) {
        chunks.push(chunk);
      }

      buffer = Buffer.concat(chunks);
      contentType = "text/csv";
      fileName = `answers-${projectId}.csv`;
    }

    // Save exported file in DB
    const file = await exportFileRepository.create({
      projectId,
      userId,
      fileName,
      fileType: String(type),
      contentType,
      fileBuffer: buffer,
    });

    return res.status(201).json({
      message: "File exported and saved",
      fileId: file.id,
    });
  } catch (err: any) {
    console.error(err);
    res.status(500).json({ message: "Export failed", error: err.message });
  }
};

// 📥 Download file by ID
export const downloadExportedFile = async (req: Request, res: Response) => {
  try {
    const fileId = Number(req.params.fileId);

    const file = await exportFileRepository.findById(fileId);
    if (!file) {
      return res.status(404).json({ message: "File not found" });
    }

    res.setHeader("Content-Type", file.contentType);
    res.setHeader(
      "Content-Disposition",
      `attachment; filename=${file.fileName}`
    );
    res.send(file.fileBuffer);
  } catch (err: any) {
    console.error(err);
    res.status(500).json({ message: "Download failed", error: err.message });
  }
};

export const findAllExportFile = async (req: UserRequest, res: Response) => {
  try {
    const projectId = Number(req.query.projectId);

    const userId = Number(req.user!.id);

    if (!projectId) {
      return res.status(400).json({
        message: "project id not passed in query",
      });
    }

    const files = await exportFileRepository.findAll(userId, projectId);

    return res.status(200).json({
      success: true,
      message: "success",
      data: { files },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error getting export files",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const DeleteExportFile = async (req: UserRequest, res: Response) => {
  try {
    const userId = Number(req.user!.id);

    const fileId = Number(req.params.id);

    if (!fileId) {
      return res.status(400).json({
        message: "file id required",
      });
    }

    await exportFileRepository.deleteFile(fileId);

    return res.status(200).json({
      success: true,
      message: "success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error deleting export files",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

// XXXXXXXXXX
// Excel/CSV injection risk 
// If user-submitted answers start with =, +, @, or -, Excel may interpret them as formulas (can lead to CSV injection).
// Escape fields starting with these characters.
// const sanitize = (value: string) =>
//   typeof value === "string" && /^[=+\-@]/.test(value)
//     ? `'${value}`
//     : value;
