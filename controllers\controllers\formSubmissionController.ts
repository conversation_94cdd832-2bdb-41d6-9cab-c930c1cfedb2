import { Request, Response } from "express";
import formSubmissionRepository from "../repositories/formSubmissionRepository";
import {
  formSubmissionSchema,
  updateFormSubmissionSchema,
} from "../validators/formSubmissionValidator";
import useragent from "useragent";
import axios from "axios";
import { z } from "zod";

// Define request interface with user
interface UserRequest extends Request {
  user?: {
    id: number;
  };
}

// Define the type for ipapi.co response
interface IpApiResponse {
  ip: string;
  city: string;
  region: string;
  country_name: string;
  error?: boolean;
  reason?: string;
}

// Helper function to get location from IP
const getLocationByIP = async (ip: string): Promise<string | null> => {
  try {
    const response = await axios.get<IpApiResponse>(
      `https://ipapi.co/${ip}/json/`
    );
    if (response.data && !response.data.error) {
      const { city, region, country_name } = response.data;
      return `${city}, ${region}, ${country_name}`;
    }
    return null;
  } catch (error) {
    console.error("Error getting location:", error);
    return null;
  }
};

// Create schema for form submission with answers
const createFormSubmissionSchema = formSubmissionSchema.extend({
  libraryTemplateId: z.number().optional(),
  answers: z
    .array(
      z.object({
        questionId: z.number(),
        value: z.any(),
        questionOptionId: z.number().optional(),
        isOtherOption: z.boolean().optional(),
        answerType: z.string().optional(),
        imageUrl: z.string().optional(),
      })
    )
    .optional(),
});

// Create a new form submission
export const createFormSubmission = async (req: UserRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    // just remove createFormSubmissionSchema, it's not used and not necessary [ recommended by Rushan]
    // YO ta milounai parxa jasto lagxa.
    // Validate request body XXXXXXXXXX
    const validationResult = formSubmissionSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: validationResult.error.errors,
      });
    }

    const data = validationResult.data;

    // Check if user has access to project or library template
    if (data.projectId) {
      const hasAccess = await formSubmissionRepository.isProjectAccessible(
        userId,
        data.projectId
      );
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "You don't have access to this project",
        });
      }
    }

    // In test environment, user-agent might be undefined
    let deviceInfo = "Test Device";
    try {
      if (req.headers["user-agent"]) {
        const agent = useragent.parse(req.headers["user-agent"]);
        deviceInfo = `${agent.family} on ${agent.os}`;
      }
    } catch (error) {
      console.error("Error parsing user agent:", error);
    }

    let location = undefined;
    try {
      const ip = req.ip || "127.0.0.1";
      // If the IP is a loopback address, use a default value
      // XXXXXXXXXX

      // Skip IP location lookup in test environment
      if (process.env.NODE_ENV !== "test") {
        const rawLocation = await getLocationByIP(ip);
        location = rawLocation ?? undefined;
      }
    } catch (error) {
      console.error("Error getting location:", error);
    }

    // Create form submission
    try {
      const formSubmission = await formSubmissionRepository.create(
        {
          projectId: data.projectId,
          status: data.status,
          metadata: data.metadata,
        },
        userId,
        deviceInfo,
        location
      );

      return res.status(200).json({
        success: true,
        message: "Form submission created successfully",
        data: { formSubmission },
      });
    } catch (createError) {
      console.error("Error in repository.create:", createError);
      throw createError; // Re-throw to be caught by the outer try-catch
    }
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error creating form submission",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get all form submissions for a project
export const getProjectFormSubmissions = async (
  req: UserRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    const projectId = Number(req.params.projectId);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    if (isNaN(projectId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid project ID",
      });
    }

    // Check if user has access to project
    const hasAccess = await formSubmissionRepository.isProjectAccessible(
      userId,
      projectId
    );
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "You don't have access to this project",
      });
    }

    // Get form submissions
    const formSubmissions = await formSubmissionRepository.findByProjectId(
      projectId
    );

    return res.status(200).json({
      success: true,
      message: "get project form success",
      data: { formSubmissions },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error retrieving form submissions",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Get a form submission by ID
export const getFormSubmission = async (req: UserRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const submissionId = Number(req.params.id);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    if (isNaN(submissionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid submission ID",
      });
    }

    // Get form submission
    const formSubmission = await formSubmissionRepository.findById(
      submissionId
    );

    if (!formSubmission) {
      return res.status(404).json({
        success: false,
        message: "Form submission not found",
      });
    }

    // Check if user has access to project
    if (formSubmission.projectId) {
      const hasAccess = await formSubmissionRepository.isProjectAccessible(
        userId,
        formSubmission.projectId
      );
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "You don't have access to this form submission",
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: "get form submission success",
      data: { formSubmission },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error retrieving form submission",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Update a form submission
export const updateFormSubmission = async (req: UserRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const submissionId = Number(req.params.id);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    if (isNaN(submissionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid submission ID",
      });
    }

    // Get form submission
    const formSubmission = await formSubmissionRepository.findById(
      submissionId
    );

    if (!formSubmission) {
      return res.status(404).json({
        success: false,
        message: "Form submission not found",
      });
    }

    // Check if user has access to project
    if (formSubmission.projectId) {
      const hasAccess = await formSubmissionRepository.isProjectAccessible(
        userId,
        formSubmission.projectId
      );
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "You don't have access to this form submission",
        });
      }
    }

    // Validate request body
    const updateSchema = updateFormSubmissionSchema.extend({
      answers: z
        .array(
          z.object({
            id: z.number().optional(),
            questionId: z.number(),
            value: z.any(),
            questionOptionId: z.number().optional(),
            isOtherOption: z.boolean().optional(),
            answerType: z.string().optional(),
            imageUrl: z.string().optional(),
          })
        )
        .optional(),
    });

    const validationResult = updateSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        message: "Validation error",
        errors: validationResult.error.errors,
      });
    }

    const data = validationResult.data;

    // Update form submission
    const updatedFormSubmission = await formSubmissionRepository.update(
      submissionId,
      {
        status: data.status ? String(data.status) : undefined,
        completedAt: data.completedAt
          ? typeof data.completedAt === "string"
            ? new Date(data.completedAt)
            : data.completedAt
          : undefined,
        durationSeconds: data.durationSeconds
          ? Number(data.durationSeconds)
          : undefined,
        metadata: data.metadata
          ? (data.metadata as Record<string, any>)
          : undefined,
        submittedAt: data.submittedAt
          ? typeof data.submittedAt === "string"
            ? new Date(data.submittedAt)
            : data.submittedAt
          : undefined,
      }
    );

    return res.status(200).json({
      success: true,
      message: "Form submission updated successfully",
      data: { formSubmission: updatedFormSubmission },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error updating form submission",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

// Delete a form submission
export const deleteFormSubmission = async (req: UserRequest, res: Response) => {
  try {
    const userId = req.user?.id;
    const submissionId = Number(req.params.id);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    if (isNaN(submissionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid submission ID",
      });
    }

    // Get form submission
    const formSubmission = await formSubmissionRepository.findById(
      submissionId
    );

    if (!formSubmission) {
      return res.status(404).json({
        success: false,
        message: "Form submission not found",
      });
    }

    // Check if user has access to project
    if (formSubmission.projectId) {
      const hasAccess = await formSubmissionRepository.isProjectAccessible(
        userId,
        formSubmission.projectId
      );
      if (!hasAccess) {
        return res.status(403).json({
          success: false,
          message: "You don't have access to this form submission",
        });
      }
    }

    // Delete form submission
    await formSubmissionRepository.delete(submissionId);

    return res.status(200).json({
      success: true,
      message: "Form submission deleted successfully",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error deleting form submission",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const ToggleFormSubmissionLoginRequired = async (
  req: UserRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    const submissionId = parseInt(req.params.id);

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: "Unauthorized",
      });
    }

    if (isNaN(submissionId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid submission ID",
      });
    }

    // Get the existing submission
    const existingSubmission = await formSubmissionRepository.findById(
      submissionId
    );

    if (!existingSubmission) {
      return res.status(404).json({
        success: false,
        message: "Form submission not found",
      });
    }

    // Check if user has access to the project
    if (!existingSubmission.projectId) {
      return res.status(403).json({
        success: false,
        message: "Submission has no associated project",
      });
    }

    const hasAccess = await formSubmissionRepository.isProjectAccessible(
      userId,
      existingSubmission.projectId
    );

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: "You don't have access to this submission",
      });
    }

    // Toggle login required
    const formSubmission = await formSubmissionRepository.changeLoginRequired(
      submissionId
    );

    return res.status(200).json({
      success: true,
      message: "Form submission login required toggled successfully",
      data: { formSubmission },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error toggling form submission login required",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const DeleteManyFormSubmission = async (
  req: UserRequest,
  res: Response
) => {
  try {
    const { ids } = req.body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: "ids must be non empty array" });
    }

    const deleted = await formSubmissionRepository.deleteMultipleSubmission(
      ids
    );

    return res.status(200).json({
      message: `${deleted.count} submission deleted`,
      deletedCount: deleted.count,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error deleting multiple forms",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
