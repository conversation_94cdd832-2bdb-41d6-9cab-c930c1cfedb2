import { Request, Response } from "express";
import { InputType, Operator } from "@prisma/client";
import libraryQuestionBlockQuestionRepository from "../repositories/libraryQuestionBlockQuestionRepository";
import { LibraryQuestionBlockQuestionSchema } from "../validators/libraryQuestionBlockQuestionValidator";
interface userRequest extends Request {
  user?: {
    id: number;
  };
}
export const createLibraryQustionBlockQuestion = async (
  req: userRequest,
  res: Response
) => {
  try {
    const {
      label,
      inputType,
      hint,
      placeholder,
      isRequired,
      position,
      questionOptions,
      conditions,
      libraryQuestionBlockQuestionGroupId,
    } = req.body;

    const userId = Number(req.user?.id); // assuming req.user exists via auth middleware

    if (!userId) {
      return res.status(401).json({ message: "User not authenticated" });
    }

    const newQuestion = await libraryQuestionBlockQuestionRepository.create(
      {
        label,
        inputType,
        hint,
        placeholder,
        isRequired,
        position,
        libraryQuestionBlockQuestionGroupId,
        questionOptions: questionOptions?.map((option: any) => ({
          label: option.label,
          code: option.code,
          nextQuestionId: option.nextQuestionId,
        })),
        conditions: conditions?.map((cond: any) => ({
          operator: cond.operator as Operator,
          value: cond.value,
        })),
      },
      userId
    );

    res.status(201).json(newQuestion);
  } catch (error) {
    console.error("Error creating question:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

export const updateLibraryQustionBlockQuestion = async (
  req: userRequest,
  res: Response
) => {
  try {
    const id = Number(req.params.id);
    const userId = req.user?.id;

    if (!userId || isNaN(id)) {
      res.status(400).json({
        success: false,
        message: "Invalid request",
      });
      return;
    }
    const existingQuestion =
      await libraryQuestionBlockQuestionRepository.findById(id);

    if (!existingQuestion) {
      res.status(404).json({
        success: false,
        message: "Question not found",
      });
      return;
    }

    // Parse the request body without requiring all fields
    const parseResult = LibraryQuestionBlockQuestionSchema.safeParse({
      ...req.body,
      // Add dummy values for any required fields that aren't in the update
      // These won't actually be used for the update, just to pass validation
      label: req.body.label || existingQuestion.label,
      inputType: req.body.inputType || existingQuestion.inputType,
      position: req.body.position || existingQuestion.position,
      userId,
    });


    if (!parseResult.success) {
      res.status(400).json({
        success: false,
        message: "Validation error",
        errors: parseResult.error.errors,
      });
      return;
    }

    // Only include fields that were actually in the request body
    // Only include fields that were actually in the request body
    const validatedData: Record<string, any> = {};
    const fields = [
      "label",
      "inputType",
      "isRequired",
      "hint",
      "placeholder",
      "position",
      "questionOptions",
      "conditions",
    ] as const;

    for (const field of fields) {
      if (req.body[field] !== undefined) {
        // Rename questionOptions to options
        if (field === "questionOptions") {
          validatedData.options = (parseResult.data as any)[field];
        } else {
          validatedData[field] = (parseResult.data as any)[field];
        }
      }
    }

    // Check if options are required for select input types
    if (
      validatedData.inputType === "selectone" ||
      validatedData.inputType === "selectmany"
    ) {
      if (
        !validatedData.options ||
        !Array.isArray(validatedData.options) ||
        validatedData.options.length === 0
      ) {
        res.status(400).json({
          success: false,
          message: "Options must be provided for select input types",
        });
        return;
      }
    }

    // Use the updated repository method to handle options and conditions
    const updatedQuestion =
      await libraryQuestionBlockQuestionRepository.updateById(
        id,
        validatedData
      );

    res.status(200).json({ message: "question updated success" });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error updating question",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const deleteLibraryQustionBlockQuestion = async (
  req: userRequest,
  res: Response
) => {
  try {
    const userId = req.user?.id;
    const id = Number(req.params.id);

    if (!userId || isNaN(id)) {
      return res.status(400).json({
        message: "Invalid request: User ID or Question ID is missing",
        success: false,
      });
    }
    const currentQuestion =
      await libraryQuestionBlockQuestionRepository.findById(id);
    if (!currentQuestion) {
      return res.status(404).json({
        message: "Question not found",
        success: false,
      });
    }

    await libraryQuestionBlockQuestionRepository.deleteQuestion(id);

    return res.status(200).json({
      message: "Successfully deleted question",
      success: true,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      message: error instanceof Error ? error.message : "unexpected error",
      success: false,
    });
  }
};

export const getAllLibraryQustionBlockQuestion = async (
  req: userRequest,
  res: Response
) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(404).json({
        success: false,
        message: "user not found",
      });
    }
    const userId = Number(req.user.id);

    const questions = await libraryQuestionBlockQuestionRepository.findAll(
      userId
    );

    return res
      .status(200)
      .json({ message: "Successfully fetched questions.", questions });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error fetching questions",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

// Zod validation use gareko xa vaney subbai thou ma tye use garam na katai use gareko xa katai xaina. 
// in this file zod validation is impmented in updateLibraryQustionBlockQuestion but not in createLibraryQustionBlockQuestion why ?
// XXXXXXXXXX