import { Request, Response } from "express";
import { prisma } from "../utils/prisma";
import {
  libraryQuestionBlockQuestionGroupSchema,
  updateLibraryQuestionBlockQuestionGroupSchema,
} from "../validators/libraryQuestionBlockQuestionGroupValidator";
import libraryQuestionBlockQuestionGroupRepository from "../repositories/libraryQuestionBlockQuestionGroupRepository";

export const createLibraryQuestionBlockQuestionGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const result = libraryQuestionBlockQuestionGroupSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }
    const questionData = result.data;

    const questionGroup =
      await libraryQuestionBlockQuestionGroupRepository.create(questionData);

    return res.status(200).json({
      success: true,
      message: "library question block question group created",
      data: { questionGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};
export const updateLibraryQuestionBlockQuestionGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const result = updateLibraryQuestionBlockQuestionGroupSchema.safeParse(
      req.body
    );
    if (!result.success) {
      return res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
    }

    const { id, title, order, parentGroupId, selectedQuestionIds } =
      result.data;

    const updates: any = {
      title,
      order,
      parentGroupId,
    };

    if (selectedQuestionIds && selectedQuestionIds.length > 0) {
      updates.questionBlockQuestion = {
        set: selectedQuestionIds.map((questionId: number) => ({
          id: questionId,
        })),
      };
    }

    const updateQuestionGroup =
      await libraryQuestionBlockQuestionGroupRepository.update(id, updates);

    return res.status(200).json({
      success: true,
      message: "Group question updated successfully",
      data: { updateQuestionGroup },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "Error updating question group",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
  }
};

export const deleteLibraryQuestionBlockQuestionGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const id = Number(req.params.id);
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "invalid id",
      });
    }
    await libraryQuestionBlockQuestionGroupRepository.delete(id);

    return res.status(200).json({
      success: true,
      message: "group deleted sucess",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error delete question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const deleteLibraryQuestionBlockQuestionAndGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const id = Number(req.params.id);
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "invalid id",
      });
    }

    await libraryQuestionBlockQuestionGroupRepository.deleteManyQuestionByGroup(
      id
    );

    await libraryQuestionBlockQuestionGroupRepository.delete(id);

    return res.status(200).json({
      success: true,
      message: "group and question related to that group are delete succesfuly",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error delete question group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const removeLibraryQuestionBlockQuestionIdFromGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId, questionId } = req.body;

    const group = await prisma.libraryQuestionBlockQuestionGroup.findUnique({
      where: { id: Number(groupId) },
      include: { questionBlockQuestion: true },
    });

    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Question group not found",
      });
    }

    const questionExists = group.questionBlockQuestion.some(
      (q) => q.id === Number(questionId)
    );
    if (!questionExists) {
      return res.status(404).json({
        success: false,
        message: "Question not found in this group",
      });
    }

    await prisma.libraryQuestionBlockQuestion.update({
      where: { id: Number(questionId) },
      data: { libraryQuestionBlockQuestionGroupId: null }, // 👈 remove its link to the group
    });

    res.status(200).json({
      success: true,
      message: "Question removed from group successfully",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error removing question from group",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateLibraryQuestionBlockQuestionFromOneGroupToAnother = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId, newGroupId, questionId } = req.body;
    if (!groupId || !newGroupId || !questionId) {
      return res.status(404).json({
        success: false,
        message: "id not found",
      });
    }

    // XXXXXXX this is unsafe, should be checked, ex: const groupid = await libraryQuestionBlockQuestionGroupRepository.findById(groupId);
    // yesto issue aru pani dherai files and thou ma xa fix it
    // it returns a Promise, not the actual result (object or null).
    const groupid =
      libraryQuestionBlockQuestionGroupRepository.findById(groupId);
    if (!groupid) {
      return res.status(404).json({
        success: false,
        message: "group id not found",
      });
    }

    const newGroupid =
      libraryQuestionBlockQuestionGroupRepository.findById(newGroupId);

    if (!newGroupid) {
      return res.status(404).json({
        success: false,
        message: "new group id not found",
      });
    }
    const question = await prisma.libraryQuestionBlockQuestion.findUnique({
      where: { id: Number(questionId) },
    });

    if (!question) {
      return res.status(404).json({
        success: false,
        message: "question id not found",
      });
    }

    if (question.libraryQuestionBlockQuestionGroupId !== Number(groupId)) {
      return res.status(400).json({
        success: false,
        message: "Question does not belong to the old group",
      });
    }

    await prisma.libraryQuestionBlockQuestion.update({
      where: { id: Number(questionId) },
      data: {
        libraryQuestionBlockQuestionGroupId: Number(newGroupId),
      },
    });

    return res.status(200).json({
      success: true,
      message: "update success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error adding question from one group to another",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateLibraryQuestionBlockOneGroupInsideAnotherGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { childGroupId, ParentGroupId } = req.body;

    const childGroupid =
      libraryQuestionBlockQuestionGroupRepository.findById(childGroupId);
    if (!childGroupid) {
      return res.status(404).json({
        success: false,
        message: "group id not found",
      });
    }

    const ParentGroupid =
      libraryQuestionBlockQuestionGroupRepository.findById(ParentGroupId);

    if (!ParentGroupid) {
      return res.status(404).json({
        success: false,
        message: "new group id not found",
      });
    }

    const update =
      await libraryQuestionBlockQuestionGroupRepository.updateGroupInsideParentGroup(
        childGroupId,
        ParentGroupId
      );

    return res.status(200).json({
      success: true,
      message: "question Group updated success",
      data: { update },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error moving group inside the parentGroup",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const removeLibraryQuestionBlockGroupFromParentGroup = async (
  req: Request,
  res: Response
) => {
  try {
    const { groupId } = req.body;
    const groupid =
      libraryQuestionBlockQuestionGroupRepository.findById(groupId);
    if (!groupid) {
      return res.status(400).json({
        success: false,
        message: "Group id is required",
      });
    }
    const group = await libraryQuestionBlockQuestionGroupRepository.findById(
      groupId
    );

    if (!group) {
      return res.status(404).json({
        success: false,
        message: "Group id not found",
      });
    }

    // Optional: Check if group has a parentGroupId
    if (!group.parentGroupId) {
      return res.status(400).json({
        success: false,
        message: "Group has no parent group to remove",
      });
    }

    await libraryQuestionBlockQuestionGroupRepository.RemoveGroupFromParentGroup(
      groupId
    );

    return res.status(200).json({
      // XXXXXXXXXX this is unsafe, should be checked
      success: false,
      message: "question remove success",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error adding question from one group to another",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

// XXXXXXXXXX please use centralized error handling