import { Request, Response } from "express";
import { ApiResponse } from "../utils/ApiResponse";
import {
  ChangeStatusSchema,
  deleteMultipleProjectSchema,
  ProjectSchema,
  updateProjectStatusesSchema,
} from "../validators/projectValidators";
import projectRepository from "../repositories/projectRepository";
import { Status } from "@prisma/client";
import { prisma } from "../utils/prisma";
import libraryTemplateRepository from "../repositories/libraryTemplateRepository";
import libraryQuestionRepository from "../repositories/libraryQuestionRepository";
import { json } from "stream/consumers"; // XXXXXXXXXX use na vako nikaldim

interface userRequest extends Request {
  user?: {
    id: number;
  };
}

export const createProject = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const userId = req.user!.id;
    const result = ProjectSchema.safeParse(req.body);

    if (!result.success) {
      res.status(400).json({
        success: false,
        message: result.error.flatten().fieldErrors,
      });
      return;
    }

    const { name, description, sector, country } = result.data;

    const existanceProject = await projectRepository.findByName(name, userId);
    if (existanceProject) {
      res
        .status(400)
        .json({ success: false, message: "project already exist" });
      return;
    }

    const project = await projectRepository.create({
      name,
      description,
      sector,
      userId,
      country,
    });

    res.status(201).json({ message: "Project created successfully", project });
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error creating project",
      error: error.message,
    });
    return;
  }
};

export const getAllProject = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  try {
    const id = Number(req.user!.id);
    if (!id) {
      res.status(404).json({
        success: false,
        message: "user id not found",
      });
    }
    const projects = await projectRepository.findAll(id);

    res
      .status(200)
      .json({ message: "Successfully fetched all projects", projects });

    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error getting projects",
      error: error.message,
    });
    return;
  }
};

export const updateProjects = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);
  try {
    const userId = req.user!.id;
    const projectuser = await projectRepository.findProjectByIdAndUser(
      id,
      userId
    );

    if (!projectuser) {
      res.status(404).json({
        success: false,
        message: "no project found",
      });

      return;
    }

    const result = ProjectSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
      return;
    }

    const updatedData = result.data;

    const updatedProject = await projectRepository.updateById(id, updatedData);
    res
      .status(200)
      .json(
        new ApiResponse(200, { updatedProject }, "Project updated success")
      );
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error updating project",
      error: error.message,
    });
    return;
  }
};

export const deleteProject = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);
  try {
    const userId = req.user!.id;
    const project = await projectRepository.findProjectByIdAndUser(id, userId);
    if (!project) {
      res.status(404).json({
        success: false,
        message: "no project found with give id",
      });
      return;
    }

    await projectRepository.deleteProject(id);

    res.status(200).json(new ApiResponse(200, {}, "project deleted success"));
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error deleting project",
      error: error.message,
    });

    return;
  }
};

export const getProjectById = async (
  req: userRequest,
  res: Response
): Promise<void> => {
  const id = Number(req.params.id);
  try {
    const userId = req.user!.id;
    const project = await projectRepository.findProjectByIdAndUser(id, userId);
    if (!project) {
      res.status(404).json({
        success: false,
        message: "project not found",
      });
      return;
    }

    res.status(200).json({ message: "Successfully fetched project", project });
    return;
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: "error getting user by id",
      error: error.message,
    });
    return;
  }
};

export const createProjectFromLibraryTemplate = async (
  req: Request & { user?: { id: number } },
  res: Response
) => {
  try {
    const userId = req.user!.id;

    const { templateId } = req.body;
    if (!templateId) {
      return res.status(400).json({
        success: false,
        message: "Template ID is not provided in the request body",
      });
    }

    const libraryTemplate = await libraryTemplateRepository.findById(
      templateId
    );

    if (!libraryTemplate) {
      res.status(404).json({
        success: false,
        message: "Library template not found",
      });
      return;
    }

    // Verify user owns the library template
    const isOwner = await libraryTemplateRepository.isOwner(userId, templateId);
    if (!isOwner) {
      res.status(403).json({
        success: false,
        message: "You don't have permission to use this library template",
      });
      return;
    }

    // Get all library questions for this template
    const libraryQuestions =
      await libraryQuestionRepository.findByLibraryTemplateId(templateId);

    // Validate project data from request body
    const result = ProjectSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        errors: result.error.flatten().fieldErrors,
      });
      return;
    }

    // Check if project with same name already exists for this user
    const existingProject = await prisma.project.findFirst({
      where: {
        name: result.data.name,
        userId: userId,
      },
    });

    if (existingProject) {
      res.status(400).json({
        success: false,
        message: "A project with this name already exists for your account",
      });
      return;
    }

    // Create a transaction to ensure all operations complete together
    const project = await prisma.$transaction(async (tx) => {
      // Create the main project
      const project = await tx.project.create({
        data: {
          name: result.data.name,
          description: result.data.description || "",
          status: Status.draft,
          userId: userId,
          sector: result.data.sector,
          country: result.data.country,
        },
      });

      // Add user as project owner
      await tx.projectUser.create({
        data: {
          projectId: project.id,
          userId: userId,
          permission: {
            isOwner: true,
            canEdit: true,
            canDelete: true,
            canView: true,
          },
        },
      });

      // Convert library questions to project questions
      if (libraryQuestions.length > 0) {
        for (const libraryQuestion of libraryQuestions) {
          // Create the project question from library question
          const question = await tx.question.create({
            data: {
              label: libraryQuestion.label,
              inputType: libraryQuestion.inputType,
              hint: libraryQuestion.hint,
              placeholder: libraryQuestion.placeholder,
              isRequired: libraryQuestion.isRequired,
              position: libraryQuestion.position,
              projectId: project.id,
            },
          });

          // Get library question options
          const libraryOptions = await tx.libraryQuestionOption.findMany({
            where: {
              libraryQuestionId: libraryQuestion.id,
            },
          });

          // Create question options if any
          if (libraryOptions.length > 0) {
            for (const option of libraryOptions) {
              await tx.questionOption.create({
                data: {
                  label: option.label,
                  code: option.code,
                  questionId: question.id,
                },
              });
            }
          }

          // Get library question conditions
          const libraryConditions = await tx.libraryQuestionCondition.findMany({
            where: {
              libraryQuestionId: libraryQuestion.id,
            },
          });

          // Create question conditions if any
          if (libraryConditions.length > 0) {
            for (const condition of libraryConditions) {
              await tx.questionCondition.create({
                data: {
                  operator: condition.operator,
                  value: condition.value,
                  questionId: question.id,
                },
              });
            }
          }
        }
      }

      return project;
    });

    res
      .status(201)
      .json(
        new ApiResponse(
          201,
          { project },
          "Project created successfully from library template"
        )
      );
    return;
  } catch (error: unknown) {
    console.error("Error creating project from template:", error);
    res.status(500).json({
      success: false,
      message: "Error creating project from library template",
      error: error instanceof Error ? error.message : "Unexpected error",
    });
    return;
  }
};

export const changeProjectStatus = async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    // XXXXXXXXXX Repetitive vaye teslai helper function banam na hai, not tye tye code subbai thou ma please DRY
    if (!id) {
      return res.status(404).json({
        success: false,
        message: "project id is required",
      });
    }

    const result = ChangeStatusSchema.safeParse(req.body);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
    }

    const updatedData = result.data;

    const updatedProject = await projectRepository.changeProjectStatus(
      id,
      updatedData.status
    );

    return res.status(200).json({
      // XXXXXXXXXX subbai thout ma tye xa mai wrong ho ki k ho ? 
      success: true,
      message: "status updated success",
      data: { updatedProject },
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const updateManyProjectStatus = async (req: Request, res: Response) => {
  try {
    const result = updateProjectStatusesSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
    }

    const updatedData = result.data;

    const updatedProject = await projectRepository.changeManyProjectStatus(
      updatedData.projectIds,
      updatedData.status
    );

    return res.status(200).json({
      success: false,
      message: "status changed success",
      data: { updatedProject },
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const DeleteMultipleProject = async (req: Request, res: Response) => {
  try {
    const result = deleteMultipleProjectSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        error: result.error.flatten(),
      });
    }

    const updatedData = result.data;

    await projectRepository.deleteMultipleProject(updatedData.projectIds);

    return res.status(200).json({
      success: false,
      message: "project deleted success",
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error deleteing project",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const fetchQuestionForForm = async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);

    if (!id) {
      return res.status(404).json({
        message: "id not found",
      });
    }

    const project = await projectRepository.fetchQuestionGroupWithQuestion(id);

    return res.status(200).json({
      success: true,
      message: "data fetch success",
      data: { project },
    });
  } catch (error: unknown) {
    res.status(500).json({
      success: false,
      message: "error getting question group and questions",
      error: error instanceof Error ? error.message : "unexpected error",
    });
    return;
  }
};

export const fetchQuestionForForms = async (req: Request, res: Response) => {
  try {
    const id = Number(req.params.id);
    if (!id) {
      return res.status(404).json({ message: "id not found" });
    }

    const project = await prisma.project.findUnique({ where: { id } });
    if (!project) {
      return res.status(404).json({ message: "project not found" });
    }

    const orderedItems = await projectRepository.fetchOrderedQuestions(id);
    console.log("project", orderedItems);

    return res.status(200).json({
      success: true,
      message: "Fetched ordered questions and groups",
      data: {
        project,
        items: orderedItems,
      },
    });
  } catch (err: any) {
    return res.status(500).json({
      success: false,
      message: "Internal server error",
      error: err.message,
    });
  }
};
