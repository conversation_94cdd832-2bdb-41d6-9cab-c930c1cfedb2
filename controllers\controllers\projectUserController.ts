import { Request, Response } from "express";
import ProjectUserRepository from "../repositories/projectUserRepository";
import { ProjectUserSchema } from "../validators/projectUserValidator";
// XXXXXXXXXX
// duplicated
import projectRepository from "../repositories/projectRepository";
import projectUserRepository from "../repositories/projectUserRepository";
import userRepository from "../repositories/userRepository";

interface userRequest extends Request {
  user?: {
    id: number;
  };
}

export const createProjectUser = async (req: userRequest, res: Response) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(404).json({
        success: false,
        message: "user not found",
      });
    }

    const owner = req.user.id;

    const result = ProjectUserSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        message: result.error.flatten().fieldErrors,
      });
      return;
    }

    const { userId, projectId, permission } = req.body;

    const user = await userRepository.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "user not found",
      });
    }

    if (userId === owner) {
      return res.status(400).json({
        success: false,
        message: "You are the owner of the project. No need to add yourself",
      });
    }

    const project = await projectRepository.findById(projectId);
    if (!project) {
      return res.status(404).json({
        success: false,
        message: "project not found",
      });
    }

    const existanceUser = await projectUserRepository.findUserProject(
      userId,
      projectId
    );

    if (existanceUser) {
      return res.status(400).json({
        success: false,
        message: "user already associated with project",
      });
    }
    const userProject = await ProjectUserRepository.create(
      userId,
      projectId,
      permission
    );

    return res.status(200).json({
      success: true,
      message: "user added successly to project", // XXXXXXXXXX Spelling milou
      data: { userProject },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      // XXXXXXXXXX
      // qustion ko spelling milena 3 thou ma check please
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const updateProjectUser = async (req: Request, res: Response) => {
  try {
    const result = ProjectUserSchema.safeParse(req.body);
    if (!result.success) {
      res.status(400).json({
        success: false,
        message: result.error.flatten().fieldErrors,
      });
      return;
    }

    const { userId, projectId, permission } = req.body;

    const updatedProject = await ProjectUserRepository.updateUserPermission(
      userId,
      projectId,
      permission
    );

    return res.status(200).json({
      // XXXXXXXXXX
      // status 200 ma kina success false xa?
      success: false,
      message: "permission updated success",
      data: { updatedProject },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating qustion",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

// XXXXXXXXXX deletProjectUser or deleteProjectUser spell fix
export const deletProjectUser = async (req: Request, res: Response) => {
  try {
    const { userId, projectId } = req.body;

    await projectUserRepository.deleteUserFromProject(userId, projectId);

    return res.status(200).json({
      // XXXXXXXXXX
      // status 200 ma kina success false xa?
      success: false,
      message: "user delete succes",
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error creating qustion",
      // XXXXXXXXXX yesto multiple error message haru xa utility function banam na ex: handleServerError(res, error, message)
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const getAllProjectUser = async (req: Request, res: Response) => {
  try {
    const  projectId  = Number(req.params.projectId); // XXXXXXXXXX this is unsafe again
    // const projectId = parseInt(req.params.projectId, 10);
    // if (isNaN(projectId)) {
    //   return res.status(400).json({ success: false, message: "Invalid projectId" });
    // }


    const AllUser = await projectUserRepository.allUsers(projectId);

    return res.status(200).json({
      success: true,
      message: "project user fetched success",
      data: { AllUser },
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error fetching users",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};

export const copyProjectUsers = async (req: userRequest, res: Response) => {
  try {
    const { sourceProjectId, targetProjectId } = req.body;

    if (!req.user || !req.user.id) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    const ownerId = req.user.id;

    // Ensure both projects exist
    const sourceProject = await projectRepository.findById(sourceProjectId);
    const targetProject = await projectRepository.findById(targetProjectId);

    if (!sourceProject || !targetProject) {
      return res.status(404).json({
        success: false,
        message: "One or both projects not found",
      });
    }

    if (targetProject?.user.id !== ownerId) {
      return res.status(403).json({
        success: false,
        message: "You are not the owner of the target project",
      });
    }

    const sourceUsers = await projectUserRepository.allUsers(sourceProjectId);

    let addedUsers = [];

    for (const projectUser of sourceUsers) {
      const { userId, permission } = projectUser;

      if (userId == ownerId) continue;

      const alreadyExist = await projectUserRepository.findUserProject(
        userId,
        targetProjectId
      );

      if (alreadyExist) continue;

      if (
        permission &&
        typeof permission === "object" &&
        !Array.isArray(permission)
      ) {
        const typedPermission = permission as Record<string, boolean>;

        const newUser = await projectUserRepository.create(
          userId,
          targetProjectId,
          typedPermission
        );
        addedUsers.push(newUser);
      }
    }

    return res.status(200).json({
      success: true,
      message: `${addedUsers.length} users copied to target project successfully.`,
      data: addedUsers,
    });
  } catch (error: unknown) {
    return res.status(500).json({
      success: false,
      message: "error copying project user",
      error: error instanceof Error ? error.message : "unexpected error",
    });
  }
};
