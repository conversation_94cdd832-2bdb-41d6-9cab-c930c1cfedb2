"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { NumericStepper } from "@/components/ui/numeric-stepper";
import { Trash, Plus, Eye, EyeOff } from "lucide-react";
import { createTable, updateTable } from "@/lib/api/table";
import { useToast } from "@/hooks/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";

interface CellValue {
  [columnId: string]: string; // Maps column ID to cell value
}

interface Column {
  id: string; // Unique identifier for the column in the UI
  columnName: string;
  parentId?: string; // Reference to parent column in the UI (string ID)
  level: number; // Nesting level (0 for top level)
  children?: Column[]; // Child columns
  // Database ID of the parent column for API submission
  // This should be the actual database ID of the parent column
  parentColumnId?: number; // Database ID of the parent column
}

interface DefaultCellValue {
  columnId: number;
  value: string;
  code?: string;
}

// Helper function to extract numeric ID from column UI ID
function extractColumnId(uiId: string): number | null {
  // First try to match the format "col-{id}" for existing columns
  const exactMatch = uiId.match(/^col-(\d+)$/);
  if (exactMatch) {
    const id = parseInt(exactMatch[1], 10);
    // Validate that the ID is within safe integer range for PostgreSQL INT4
    if (id > 0 && id <= 2147483647) {
      return id;
    }
  }

  // If that fails, try to match "col-{id}-{random}" for new columns
  const newColMatch = uiId.match(/^col-(\d+)-\d+$/);
  if (newColMatch) {
    const id = parseInt(newColMatch[1], 10);
    // Validate that the ID is within safe integer range for PostgreSQL INT4
    if (id > 0 && id <= 2147483647) {
      return id;
    }
  }

  // If all else fails, extract any number from the ID
  const anyNumberMatch = uiId.match(/^col-(\d+)/);
  if (anyNumberMatch) {
    const id = parseInt(anyNumberMatch[1], 10);
    // Validate that the ID is within safe integer range for PostgreSQL INT4
    if (id > 0 && id <= 2147483647) {
      return id;
    }
  }

  console.warn(`Invalid or unsafe column ID: ${uiId}`);
  return null;
}

interface TableQuestionBuilderProps {
  projectId: number;
  onTableCreated?: (tableId: number) => void;
  isInModal?: boolean; // Flag to indicate if component is used inside a modal
  isEditMode?: boolean; // Flag to indicate if component is used for editing
  existingTableData?: {
    id: number;
    label: string;
    tableColumns: {
      id: number;
      columnName: string;
      parentColumnId?: number;
      childColumns?: {
        id: number;
        columnName: string;
        parentColumnId: number;
      }[];
    }[];
    tableRows: {
      id: number;
      rowsName: string;
      defaultValues?: DefaultCellValue[];
    }[];
    cellValues?: Record<string, any>; // Cell values from the API
  };
}

export function TableQuestionBuilder({
  projectId,
  onTableCreated,
  isInModal = false,
  isEditMode = false,
  existingTableData,
}: TableQuestionBuilderProps) {
  const { toast } = useToast();
  const [label, setLabel] = useState(existingTableData?.label || "");
  const [columns, setColumns] = useState<Column[]>(() => {
    if (existingTableData?.tableColumns) {
      // Convert existing columns to the format needed for the UI
      const formattedColumns: Column[] = [];

      // Process parent columns first and add them to the formatted array
      existingTableData.tableColumns.forEach((parentCol) => {
        // Create the parent column
        const parentColumn: Column = {
          id: `col-${parentCol.id}`, // Use the actual database ID in the UI ID
          columnName: parentCol.columnName,
          level: 0,
          parentColumnId: undefined,
        };

        // Add parent column to the formatted array
        formattedColumns.push(parentColumn);

        console.log(
          `Added parent column: "${parentCol.columnName}" (ID: ${parentCol.id})`
        );

        // Process child columns if they exist and add them right after the parent
        if (parentCol.childColumns && parentCol.childColumns.length > 0) {
          console.log(
            `Processing ${parentCol.childColumns.length} child columns for parent "${parentCol.columnName}"`
          );

          parentCol.childColumns.forEach((childCol) => {
            const childColumn: Column = {
              id: `col-${childCol.id}`,
              columnName: childCol.columnName,
              level: 1, // Child columns are always level 1
              parentId: `col-${parentCol.id}`, // Reference to parent's UI ID
              parentColumnId: parentCol.id, // Reference to parent's database ID
            };

            // Add child column to the formatted array
            formattedColumns.push(childColumn);

            console.log(
              `Added child column: "${childCol.columnName}" (ID: ${childCol.id}) with parent "${parentCol.columnName}" (ID: ${parentCol.id})`
            );
          });
        }
      });

      console.log("Final formatted columns for UI:", formattedColumns);

      return formattedColumns.length > 0
        ? formattedColumns
        : [{ id: "col-1", columnName: "", level: 0 }];
    }

    return [{ id: "col-1", columnName: "", level: 0 }];
  });

  const [rows, setRows] = useState<
    {
      id?: number;
      rowsName: string;
      defaultValues?: DefaultCellValue[];
      cellValues?: CellValue;
    }[]
  >(() => {
    if (
      existingTableData?.tableRows &&
      existingTableData.tableRows.length > 0
    ) {
      return existingTableData.tableRows.map((row) => {
        // Initialize cell values from API data if available
        const cellValues: CellValue = {};

        if (existingTableData.cellValues) {
          console.log(
            "Processing cell values for row:",
            row.id,
            existingTableData.cellValues
          );
          // Convert API cell values to the format expected by the component
          Object.entries(existingTableData.cellValues).forEach(
            ([key, value]: [string, any]) => {
              const [columnId, rowId] = key.split("_");
              if (parseInt(rowId) === row.id) {
                // Convert database column ID to UI column ID format
                const uiKey = `col-${columnId}`;
                // Handle both object and string value formats
                const cellValue =
                  typeof value === "object" && value !== null
                    ? value.value || ""
                    : value || "";
                cellValues[uiKey] = cellValue;
                console.log(`Setting cell value: ${uiKey} = ${cellValue}`);
              }
            }
          );
        }

        console.log("Final cell values for row:", row.id, cellValues);

        return {
          id: row.id,
          rowsName: row.rowsName,
          defaultValues: row.defaultValues || [],
          cellValues: cellValues,
        };
      });
    }
    // Start with one empty row
    return [{ rowsName: "1", defaultValues: [], cellValues: {} }];
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPreview, setShowPreview] = useState(true);

  // Generate a unique ID for new columns using a safer approach
  // Use a counter-based approach to avoid large timestamp values
  const generateId = (() => {
    let counter = 1;
    return () => {
      // Use a smaller base number and increment counter
      // This ensures IDs stay within safe integer range
      const baseId = 100000 + counter;
      counter++;
      return `col-${baseId}-${Math.floor(Math.random() * 100)}`;
    };
  })();

  // Function to handle cell value changes
  const handleCellValueChange = (
    rowIndex: number,
    columnId: string,
    value: string
  ) => {
    setRows((prevRows) => {
      const newRows = [...prevRows];
      if (!newRows[rowIndex]) return prevRows;
      if (!newRows[rowIndex].cellValues) {
        newRows[rowIndex].cellValues = {};
      }
      newRows[rowIndex].cellValues = {
        ...newRows[rowIndex].cellValues,
        [columnId]: value,
      };
      return newRows;
    });
  };

  // Function to get a cell's value
  const getCellValue = (rowIndex: number, columnId: string): string => {
    return rows[rowIndex]?.cellValues?.[columnId] || "";
  };

  // Function to add a new row
  const handleAddRow = () => {
    setRows((prevRows) => [
      ...prevRows,
      {
        rowsName: `${prevRows.length + 1}`, // Keep this for backend compatibility
        defaultValues: [],
        cellValues: {},
      },
    ]);
  };

  // Function to handle row count changes from NumericStepper
  const handleRowCountChange = (newCount: number) => {
    setRows((prevRows) => {
      const currentCount = prevRows.length;

      if (newCount === currentCount) {
        return prevRows; // No change needed
      }

      if (newCount > currentCount) {
        // Add new rows
        const newRows = [...prevRows];
        for (let i = currentCount; i < newCount; i++) {
          newRows.push({
            rowsName: `${i + 1}`,
            defaultValues: [],
            cellValues: {},
          });
        }
        return newRows;
      } else {
        // Remove rows from the end
        return prevRows.slice(0, newCount);
      }
    });
  };

  // Add a top-level column

  // Table Preview JSX
  // Place this in your component's return, replacing the old table preview if present
  // (If you have a previous table preview, replace it with this block)

  /* Table Preview Section Start */
  <div className="mt-6 space-y-4">
    <div className="flex justify-between items-center">
      <h4 className="font-medium">Table Preview</h4>
      <NumericStepper
        value={rows.length}
        onChange={handleRowCountChange}
        min={0}
        max={1000}
        label="Rows"
        hideInput={true}
        className="flex-shrink-0"
      />
    </div>
    <div className="border rounded-md overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px] text-center">S.NO</TableHead>
            {columns.map((column) => (
              <TableHead key={column.id}>
                {column.columnName || `Column ${column.id}`}
              </TableHead>
            ))}
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {rows.map((row, rowIndex) => (
            <TableRow key={rowIndex}>
              <TableCell className="font-medium text-center bg-blue-50/50">
                {rowIndex + 1}
              </TableCell>
              {columns.map((column) => (
                <TableCell key={`${rowIndex}-${column.id}`}>
                  <Input
                    value={getCellValue(rowIndex, column.id)}
                    onChange={(e) =>
                      handleCellValueChange(rowIndex, column.id, e.target.value)
                    }
                    placeholder="Value"
                    className="w-full"
                  />
                </TableCell>
              ))}
              <TableCell>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveRow(rowIndex)}
                  disabled={rows.length <= 1}
                  title="Remove row"
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  </div>;
  /* Table Preview Section End */

  const handleAddColumn = () => {
    setColumns([
      ...columns,
      {
        id: generateId(),
        columnName: "",
        level: 0,
      },
    ]);
  };

  // Add a child column to a parent column
  const handleAddChildColumn = (parentId: string) => {
    // Find the parent column and its level
    const parentColumn = findColumnById(parentId);
    if (!parentColumn) return;

    // Check if parent is already a child column (level > 0)
    // If so, we can't add a child to it (would create a 3rd level)
    if (parentColumn.level > 0) {
      toast({
        title: "Cannot add child column",
        description:
          "Cannot create more than 2 levels of nested columns (parent → child → grandchild)",
        variant: "destructive",
      });
      return;
    }

    // Count existing children of this parent
    const childCount = columns.filter(
      (col) => col.parentId === parentId
    ).length;

    // Check if parent already has 2 children
    if (childCount >= 2) {
      toast({
        title: "Cannot add more child columns",
        description: `Parent column "${
          parentColumn.columnName || "Unnamed"
        }" cannot have more than 2 child columns`,
        variant: "destructive",
      });
      return;
    }

    // Extract the parent's database ID from its UI ID
    const parentDbIdMatch = parentId.match(/^col-(\d+)/);
    const parentDbId = parentDbIdMatch
      ? parseInt(parentDbIdMatch[1], 10)
      : undefined;

    const newColumn: Column = {
      id: generateId(),
      columnName: "",
      parentId: parentId, // This is the UI ID of the parent
      level: parentColumn.level + 1,
      // Store the parent's database ID for API submission
      parentColumnId: parentDbId,
    };

    // Insert the new column after the parent and its existing children
    const newColumns = [...columns];
    const lastChildIndex = findLastChildIndex(parentId);

    // If there are no existing children, insert right after the parent
    if (
      lastChildIndex === -1 ||
      lastChildIndex === columns.findIndex((col) => col.id === parentId)
    ) {
      const parentIndex = columns.findIndex((col) => col.id === parentId);
      newColumns.splice(parentIndex + 1, 0, newColumn);
    } else {
      // Otherwise, insert after the last child
      newColumns.splice(lastChildIndex + 1, 0, newColumn);
    }

    setColumns(newColumns);
  };

  // Find a column by its ID
  const findColumnById = (id: string): Column | undefined => {
    return columns.find((col) => col.id === id);
  };

  // Find the index of the last child of a parent column
  const findLastChildIndex = (parentId: string): number => {
    const parentIndex = columns.findIndex((col) => col.id === parentId);
    if (parentIndex === -1) return -1;

    // Find the last consecutive child
    let lastChildIndex = parentIndex;
    let foundChild = false;

    for (let i = parentIndex + 1; i < columns.length; i++) {
      if (columns[i].parentId === parentId) {
        // Direct child of this parent
        lastChildIndex = i;
        foundChild = true;
      } else if (isDescendantOf(columns[i], parentId)) {
        // Descendant (could be a grandchild)
        lastChildIndex = i;
        foundChild = true;
      } else {
        // Not a descendant, we've reached the end of this parent's children
        break;
      }
    }

    // If no children were found, return the parent index
    return foundChild ? lastChildIndex : parentIndex;
  };

  // Check if a column is a descendant of a parent column
  const isDescendantOf = (column: Column, ancestorId: string): boolean => {
    if (column.parentId === ancestorId) return true;
    if (!column.parentId) return false;

    const parent = findColumnById(column.parentId);
    if (!parent) return false;

    return isDescendantOf(parent, ancestorId);
  };

  // Function to remove a column and its children
  const handleRemoveColumn = (columnId: string) => {
    // Don't allow removing the last column
    if (columns.length <= 1) {
      toast({
        title: "Cannot remove column",
        description: "You must have at least one column in the table.",
        variant: "destructive",
      });
      return;
    }

    // Find all child columns that need to be removed
    const childrenToRemove = columns
      .filter((col) => {
        // Direct children
        if (col.parentId === columnId) return true;
        // Check for descendants (grandchildren, etc.)
        return isDescendantOf(col, columnId);
      })
      .map((col) => col.id);

    // Create a new array without the column and its children
    const newColumns = columns.filter((col) => {
      return col.id !== columnId && !childrenToRemove.includes(col.id);
    });

    // Update the columns state
    setColumns(newColumns);
  };

  const handleRemoveRow = (index: number) => {
    // Allow removing all rows since rows are now optional
    const newRows = [...rows];
    newRows.splice(index, 1);
    setRows(newRows);
  };

  const handleColumnNameChange = (id: string, value: string) => {
    const newColumns = columns.map((col) => {
      if (col.id === id) {
        return { ...col, columnName: value };
      }
      return col;
    });
    setColumns(newColumns);
  };

  const handleRowNameChange = (index: number, value: string) => {
    const newRows = [...rows];
    newRows[index].rowsName = value;
    setRows(newRows);
  };

  // Prepare columns for API submission by converting the hierarchical structure
  const prepareColumnsForSubmission = () => {
    // Check if we're in edit mode with existing table data
    const isEditing = isEditMode || existingTableData?.id;

    // Create a map of UI column IDs to their database IDs
    const columnIdMap = new Map<string, number>();

    // Create a map to track parent-child relationships
    const parentChildMap = new Map<string, string[]>();

    // Build the parent-child relationship map from the current UI state
    columns.forEach((col) => {
      if (col.parentId) {
        if (!parentChildMap.has(col.parentId)) {
          parentChildMap.set(col.parentId, []);
        }
        parentChildMap.get(col.parentId)?.push(col.id);
      }
    });

    // If editing, try to extract existing database IDs from the column IDs
    if (isEditing && existingTableData?.tableColumns) {
      // Map existing column IDs from the database
      columns.forEach((col) => {
        if (col.columnName.trim()) {
          // Extract the database ID from the UI ID (format: "col-{id}")
          const match = col.id.match(/^col-(\d+)/);
          if (match && match[1]) {
            const dbId = parseInt(match[1], 10);
            // Verify this ID exists in the original data
            const existingColumn = existingTableData.tableColumns.find(
              (ec) => ec.id === dbId
            );
            if (existingColumn) {
              columnIdMap.set(col.id, dbId);
            }
          }
        }
      });
    }

    // For columns without mapped IDs (new columns), assign temporary IDs
    let nextId =
      Math.max(
        ...Array.from(columnIdMap.values(), (id) => id || 0),
        ...(existingTableData?.tableColumns?.map((col) => col.id) || [0]),
        0
      ) + 1;

    // Assign IDs to columns that don't have them yet
    columns.forEach((col) => {
      if (col.columnName.trim() && !columnIdMap.has(col.id)) {
        columnIdMap.set(col.id, nextId++);
      }
    });

    // Create API columns array with proper ordering
    const apiColumns: {
      id?: number;
      columnName: string;
      parentColumnId?: number;
    }[] = [];

    // Create a map to track which UI column ID corresponds to which position in the API columns array
    const apiColumnPositionMap = new Map<string, number>();

    // Process columns in order of their level (0 = top level, 1 = first child level, etc.)
    // This ensures that parent columns are processed before their children
    // Use 0 as fallback if there are no columns with levels
    const maxLevel = Math.max(...columns.map((col) => col.level || 0), 0);

    // First pass: Add all top-level columns (level 0) to establish their positions
    const topLevelColumns = columns.filter(
      (col) => col.level === 0 && col.columnName.trim()
    );

    // Add all top-level columns first
    topLevelColumns.forEach((col) => {
      const columnId = columnIdMap.get(col.id);

      const apiColumn: {
        id?: number;
        columnName: string;
        parentColumnId?: number;
      } = {
        columnName: col.columnName.trim(),
      };

      // If editing and we have a column ID, include it
      if (isEditing && columnId) {
        apiColumn.id = columnId;
      }

      // Add the column to the API columns array
      const apiColumnIndex = apiColumns.length;
      apiColumns.push(apiColumn);

      // Store the mapping between UI column ID and its position in the API columns array
      apiColumnPositionMap.set(col.id, apiColumnIndex);
    });

    // Second pass: Add all child columns (level > 0)
    for (let level = 1; level <= maxLevel; level++) {
      // Get all columns at this level with valid names
      const columnsAtLevel = columns.filter(
        (col) => col.level === level && col.columnName.trim()
      );

      // Process each column at this level
      columnsAtLevel.forEach((col) => {
        const columnId = columnIdMap.get(col.id);

        // Create the API column object
        const apiColumn: {
          id?: number;
          columnName: string;
          parentColumnId?: number;
        } = {
          columnName: col.columnName.trim(),
        };

        // If editing and we have a column ID, include it
        if (isEditing && columnId) {
          apiColumn.id = columnId;
        }

        // If this is a child column (has a parentId), set the parentColumnId
        if (col.parentId) {
          if (isEditing) {
            // For editing, use database IDs for parentColumnId
            const parentColumn = columns.find((c) => c.id === col.parentId);
            const parentDbId = columnIdMap.get(col.parentId);

            if (parentDbId) {
              // Use the parent's database ID
              apiColumn.parentColumnId = parentDbId;
            } else {
              console.warn(
                `Could not find parent DB ID for column "${col.columnName}" (parentId: ${col.parentId})`
              );
              // Don't set a parent ID if we can't find the parent - make it a top-level column
              toast({
                title: "Warning",
                description: `Column "${col.columnName}" had a missing parent reference and was converted to a top-level column.`,
                variant: "destructive",
              });
            }
          } else {
            // For creation, use position-based indices (1-based)
            const parentPosition = apiColumnPositionMap.get(col.parentId);

            if (parentPosition !== undefined) {
              // Use the parent's position (1-based index)
              apiColumn.parentColumnId = parentPosition + 1; // Convert to 1-based index
              console.log(
                `Setting parentColumnId for "${col.columnName}" to ${
                  parentPosition + 1
                } (parent: "${
                  columns.find((c) => c.id === col.parentId)?.columnName
                }")`
              );
            } else {
              console.warn(
                `Could not find parent position for column "${col.columnName}" (parentId: ${col.parentId})`
              );
              // Don't set a parent ID if we can't find the parent - make it a top-level column
              toast({
                title: "Warning",
                description: `Column "${col.columnName}" had a missing parent reference and was converted to a top-level column.`,
                variant: "destructive",
              });
            }
          }
        }

        // Add the column to the API columns array
        const apiColumnIndex = apiColumns.length;
        apiColumns.push(apiColumn);

        // Store the mapping between UI column ID and its position in the API columns array
        apiColumnPositionMap.set(col.id, apiColumnIndex);
      });
    }

    // Validate all parent column references before returning
    const invalidParentReferences = apiColumns.filter((col) => {
      if (col.parentColumnId === undefined) {
        return false; // No parent, so it's valid
      }

      if (isEditing) {
        // For editing, parentColumnId should be a valid database ID
        // It must be a positive number
        if (col.parentColumnId <= 0) {
          return true; // Invalid if not a positive number
        }
      } else {
        // For creation, parentColumnId should be a valid position (1-based index)
        // It must be a positive number and not greater than the number of columns
        if (col.parentColumnId <= 0 || col.parentColumnId > apiColumns.length) {
          return true; // Invalid position
        }

        // Check if the referenced parent is itself a child column
        const parentColumn = apiColumns[col.parentColumnId - 1]; // Convert to 0-based index
        if (parentColumn && parentColumn.parentColumnId !== undefined) {
          return true; // Invalid: parent is itself a child (would create a 3rd level)
        }
      }

      return false;
    });

    if (invalidParentReferences.length > 0) {
      console.error(
        "Found invalid parent column references:",
        invalidParentReferences
      );

      // Fix the invalid references by removing them
      invalidParentReferences.forEach((col) => {
        col.parentColumnId = undefined;
      });

      toast({
        title: "Warning",
        description: `Fixed ${invalidParentReferences.length} invalid column relationships. Some child columns were converted to top-level columns.`,
        variant: "destructive",
      });
    }

    // Log the final columns for debugging
    console.log("Final API columns:", apiColumns);
    return apiColumns;
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    // Validate inputs
    if (!label.trim()) {
      toast({
        title: "Error",
        description: "Please enter a table label",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare columns for API submission
      const apiColumns = prepareColumnsForSubmission();

      // Ensure we have at least one row
      let rowsToProcess = rows;
      if (rowsToProcess.length === 0) {
        // Add a default row if none exists
        rowsToProcess = [
          { rowsName: "Row 1", defaultValues: [], cellValues: {} },
        ];
        console.log("No valid rows found, adding a default row");
      }

      // Prepare rows with trimmed values and proper IDs
      // Sort rows to maintain consistent order
      const sortedValidRows = [...rowsToProcess].sort((a, b) => {
        // If both rows have IDs, sort by ID
        if (a.id && b.id) {
          return a.id - b.id;
        }
        // If only one has an ID, put the one with ID first
        if (a.id) return -1;
        if (b.id) return 1;
        // Otherwise, maintain the order they appear in the UI
        return rowsToProcess.indexOf(a) - rowsToProcess.indexOf(b);
      });

      console.log("Processing rows for submission:", sortedValidRows);

      const apiRows = sortedValidRows.map((row, rowIndex) => {
        const rowData: {
          id?: number;
          rowsName: string;
          defaultValues?: DefaultCellValue[];
        } = {
          rowsName: row.rowsName.trim() || `Row ${rowIndex + 1}`,
        };

        // Only include ID if it's a valid existing ID
        if (isEditMode || existingTableData?.id) {
          if (
            row.id &&
            existingTableData?.tableRows?.some((r) => r.id === row.id)
          ) {
            rowData.id = row.id;
          }
        }

        // Convert cell values to defaultValues format for the API
        const defaultValues: DefaultCellValue[] = [];

        // Process cell values from the UI inputs
        if (row.cellValues) {
          Object.entries(row.cellValues).forEach(([columnId, value]) => {
            // Skip the S.NO column (which is non-editable) but include empty values for deletion
            if (!columnId.includes("s-no")) {
              const column = columns.find((col) => col.id === columnId);
              if (column) {
                // For new columns, we need to use the position in the apiColumns array
                let columnDbId: number | null = null;

                if (isEditMode || existingTableData?.id) {
                  // For edit mode, extract the database ID
                  columnDbId = extractColumnId(column.id);
                } else {
                  // For creation mode, use the position in the apiColumns array
                  const apiColumnIndex = apiColumns.findIndex(
                    (apiCol) => apiCol.columnName === column.columnName.trim()
                  );

                  if (apiColumnIndex !== -1) {
                    // Use 1-based index for the API
                    columnDbId = apiColumnIndex + 1;
                    console.log(
                      `Using position-based index ${columnDbId} for column "${column.columnName}"`
                    );
                  }
                }

                if (columnDbId) {
                  const trimmedValue = String(value || "").trim();
                  const defaultValue = {
                    columnId: columnDbId,
                    value: trimmedValue,
                    code: trimmedValue, // Use the same value for code
                  };
                  console.log(
                    `Adding default value for column ${columnDbId}:`,
                    defaultValue
                  );
                  console.log(
                    `Original UI column ID: ${columnId}, extracted DB ID: ${columnDbId}, column name: ${column.columnName}`
                  );
                  defaultValues.push(defaultValue);
                } else {
                  console.warn(
                    `Could not extract column DB ID from UI ID: ${columnId} for column: ${column.columnName}`
                  );
                }
              }
            }
          });
        }

        // Also include any existing default values that might be present
        if (row.defaultValues && row.defaultValues.length > 0) {
          row.defaultValues.forEach((dv) => {
            // Only add if not already added from cellValues
            if (
              !defaultValues.some(
                (existingDv) => existingDv.columnId === dv.columnId
              )
            ) {
              defaultValues.push(dv);
            }
          });
        }

        console.log(
          `Row ${rowData.rowsName} has ${defaultValues.length} default values before mapping`
        );

        // Process and validate default values
        if (defaultValues.length > 0) {
          // Filter out any invalid default values but keep empty values for deletion
          const validDefaultValues = defaultValues.filter((dv) => {
            const isValid =
              dv.columnId && typeof dv.columnId === "number" && dv.columnId > 0;

            if (!isValid) {
              console.warn(`Filtering out invalid default value:`, dv);
            }
            return isValid;
          });

          console.log(
            `Row ${rowData.rowsName} has ${validDefaultValues.length} valid default values after filtering`
          );

          // Set the default values on the row data (including empty values for deletion)
          if (validDefaultValues.length > 0) {
            rowData.defaultValues = validDefaultValues;
            console.log(
              `Final default values for row ${rowData.rowsName}:`,
              JSON.stringify(validDefaultValues)
            );
          } else {
            console.warn(
              `No valid default values found for row ${rowData.rowsName}`
            );
          }
        }

        return rowData;
      });

      let table;

      // Check if we're editing an existing table or creating a new one
      if (existingTableData?.id) {
        // Log the final payload being sent to the API
        console.log("=== FINAL API PAYLOAD FOR UPDATE ===");
        console.log("Table ID:", existingTableData.id);
        console.log("Label:", label.trim());
        console.log("API Columns:", JSON.stringify(apiColumns, null, 2));
        console.log("API Rows:", JSON.stringify(apiRows, null, 2));
        console.log("=== END PAYLOAD ===");

        // Update existing table
        table = await updateTable(
          existingTableData.id,
          label.trim(),
          apiColumns,
          apiRows
        );

        toast({
          title: "Success",
          description: "Table question updated successfully",
        });
      } else {
        // Create new table
        table = await createTable(label.trim(), projectId, apiColumns, apiRows);

        toast({
          title: "Success",
          description: "Table question created successfully",
        });

        // Reset form for new table creation
        setLabel("");
        setColumns([
          {
            id: generateId(),
            columnName: "",
            level: 0,
          },
        ]);
        setRows([]); // Start with no rows since they're optional
      }

      // Notify parent component
      if (onTableCreated && table?.id) {
        onTableCreated(table.id);
      }
    } catch (error: any) {
      console.error("Error with table operation:", error);

      // Extract more detailed error message if available
      let errorMessage = existingTableData?.id
        ? "Failed to update table question"
        : "Failed to create table question";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Create a ref for the table builder component
  const tableBuilderRef = React.useRef<HTMLDivElement>(null);

  // Add event listener for the custom submitTable event
  useEffect(() => {
    if (isInModal) {
      const handleSubmitEvent = (event: Event) => {
        // Prevent the event from bubbling further if needed
        if (event.cancelable) {
          event.stopPropagation();
        }

        // Call the submit handler
        handleSubmit();
      };

      // Add event listener to multiple possible elements

      // 1. Add to the component's root element
      if (tableBuilderRef.current) {
        const tableBuilder = tableBuilderRef.current;

        tableBuilder.addEventListener("submitTable", handleSubmitEvent);
      }

      // 2. Add to document for bubbled events
      document.addEventListener("submitTable", handleSubmitEvent);

      // 3. Add to any existing table-question-builder elements
      const allTableBuilders = document.querySelectorAll(
        ".table-question-builder"
      );

      allTableBuilders.forEach((element) => {
        element.addEventListener("submitTable", handleSubmitEvent);
      });

      return () => {
        // Clean up when component unmounts

        if (tableBuilderRef.current) {
          tableBuilderRef.current.removeEventListener(
            "submitTable",
            handleSubmitEvent
          );
        }

        document.removeEventListener("submitTable", handleSubmitEvent);

        allTableBuilders.forEach((element) => {
          element.removeEventListener("submitTable", handleSubmitEvent);
        });
      };
    }
  }, [isInModal, handleSubmit, columns]);

  return (
    <div
      ref={tableBuilderRef}
      className="space-y-6 p-4 border border-gray-200 rounded-md w-full table-question-builder"
    >
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {isEditMode || existingTableData?.id
            ? "Edit Table Question"
            : "Create Table Question"}
        </h3>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowPreview(!showPreview)}
          className="flex items-center gap-1"
        >
          {showPreview ? (
            <>
              <EyeOff className="h-4 w-4" />
              Hide Preview
            </>
          ) : (
            <>
              <Eye className="h-4 w-4" />
              Show Preview
            </>
          )}
        </Button>
      </div>

      <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200">
        <p className="font-medium mb-1">Table Structure Guidelines:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>
            Create multiple <span className="font-medium">parent columns</span>{" "}
            using the "Add Top-Level Column" button
          </li>
          <li>
            Add up to 2 <span className="font-medium">child columns</span> under
            each parent using the "+" button
          </li>
          <li>
            Child columns cannot have their own children (maximum 2 levels)
          </li>
        </ul>
      </div>

      {isInModal ? (
        <div className="space-y-4">
          <div>
            <Label htmlFor="table-label">Table Label</Label>
            <Input
              id="table-label"
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              placeholder="Enter table question label"
              required
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Columns</Label>
                {columns.map((column) => (
                  <div
                    key={column.id}
                    className={cn(
                      "flex items-center gap-2 p-2 rounded-md",
                      column.level === 0
                        ? "bg-gray-50"
                        : "bg-white border-l-2 border-gray-300"
                    )}
                    style={{ marginLeft: `${column.level * 20}px` }}
                  >
                    <div className="flex-1 flex items-center gap-2">
                      {column.level > 0 && (
                        <div className="w-4 h-4 flex items-center justify-center">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        </div>
                      )}
                      <Input
                        value={column.columnName}
                        onChange={(e) =>
                          handleColumnNameChange(column.id, e.target.value)
                        }
                        placeholder={`${
                          column.level === 0 ? "Parent" : "Child"
                        } Column`}
                        className={cn(
                          "flex-1",
                          column.level === 0
                            ? "border-blue-200 bg-white"
                            : "border-dashed"
                        )}
                      />
                      {column.level === 0 && (
                        <div className="text-xs text-blue-500 font-medium">
                          Parent
                        </div>
                      )}
                      {column.level > 0 && (
                        <div className="text-xs text-gray-500 font-medium">
                          Child
                        </div>
                      )}
                    </div>
                    <div className="flex items-center">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleAddChildColumn(column.id)}
                        title={
                          column.level > 0
                            ? "Child columns cannot have their own children"
                            : columns.filter(
                                (col) => col.parentId === column.id
                              ).length >= 2
                            ? "Maximum 2 child columns allowed"
                            : "Add child column"
                        }
                        disabled={
                          column.level > 0 || // Disable if this is already a child column
                          columns.filter((col) => col.parentId === column.id)
                            .length >= 2 // Disable if already has 2 children
                        }
                      >
                        <Plus
                          className={cn(
                            "h-4 w-4",
                            (column.level > 0 ||
                              columns.filter(
                                (col) => col.parentId === column.id
                              ).length >= 2) &&
                              "text-gray-300"
                          )}
                        />
                      </Button>

                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveColumn(column.id)}
                        disabled={columns.length <= 1}
                        title="Remove column"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddColumn}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Top-Level Column
                </Button>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">
                    Table Rows
                  </Label>
                  <NumericStepper
                    value={rows.length}
                    onChange={handleRowCountChange}
                    min={0}
                    max={1000}
                    hideInput={true}
                    className="flex-shrink-0"
                  />
                </div>
                <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded-md border border-blue-200">
                  <div className="flex items-center justify-between">
                    <span>
                      Rows will be automatically numbered (1, 2, 3...)
                    </span>
                    <span className="font-medium text-blue-700">
                      {rows.length} {rows.length === 1 ? "row" : "rows"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {showPreview && (
              <div className="space-y-2">
                <Label>Table Preview</Label>
                <div className="border rounded-md p-4 overflow-x-auto max-h-[300px] overflow-y-auto">
                  <Table>
                    <TableHeader>
                      {/* Calculate the maximum nesting level */}
                      {(() => {
                        const maxLevel = Math.max(
                          ...columns.map((col) => col.level),
                          0
                        );

                        // Create header rows for each level
                        const headerRows = [];

                        // First row with parent columns starting from leftmost position
                        headerRows.push(
                          <TableRow key="header-row-0">
                            {/* Render top-level (parent) columns starting from position 1 */}
                            {columns
                              .filter((col) => col.level === 0)
                              .map((parentCol) => {
                                // Count all descendants at all levels
                                const getAllDescendants = (
                                  colId: string
                                ): Column[] => {
                                  const directChildren = columns.filter(
                                    (c) => c.parentId === colId
                                  );
                                  let allDescendants = [...directChildren];

                                  directChildren.forEach((child) => {
                                    allDescendants = [
                                      ...allDescendants,
                                      ...getAllDescendants(child.id),
                                    ];
                                  });

                                  return allDescendants;
                                };

                                const descendants = getAllDescendants(
                                  parentCol.id
                                );
                                const leafNodes = descendants.filter(
                                  (d) =>
                                    !columns.some((c) => c.parentId === d.id)
                                );

                                // If no descendants, it's a leaf node itself
                                const colSpanCount =
                                  descendants.length > 0
                                    ? leafNodes.length || 1
                                    : 1;

                                return (
                                  <TableHead
                                    key={parentCol.id}
                                    colSpan={colSpanCount}
                                    className="text-center border-b"
                                  >
                                    {parentCol.columnName || "Column"}
                                  </TableHead>
                                );
                              })}
                          </TableRow>
                        );

                        // Create header rows for each additional level
                        for (let level = 1; level <= maxLevel; level++) {
                          headerRows.push(
                            <TableRow key={`header-row-${level}`}>
                              {/* For each level, we need to find columns at that level and their parents */}
                              {(() => {
                                const columnsAtLevel = columns.filter(
                                  (col) => col.level === level - 1
                                );

                                return columnsAtLevel.map((col) => {
                                  // Get direct children of this column
                                  const children = columns.filter(
                                    (c) => c.parentId === col.id
                                  );

                                  // If no children, render an empty cell to maintain alignment
                                  if (children.length === 0) {
                                    return (
                                      <TableHead
                                        key={`empty-${col.id}`}
                                        className="text-center border-b"
                                      >
                                        {/* Empty cell to maintain column alignment */}
                                      </TableHead>
                                    );
                                  }

                                  // Render each child with appropriate colspan
                                  return children.map((child) => {
                                    // Calculate how many leaf nodes are under this child
                                    const getAllDescendants = (
                                      colId: string
                                    ): Column[] => {
                                      const directChildren = columns.filter(
                                        (c) => c.parentId === colId
                                      );
                                      let allDescendants = [...directChildren];

                                      directChildren.forEach((child) => {
                                        allDescendants = [
                                          ...allDescendants,
                                          ...getAllDescendants(child.id),
                                        ];
                                      });

                                      return allDescendants;
                                    };

                                    const descendants = getAllDescendants(
                                      child.id
                                    );
                                    const leafNodes = descendants.filter(
                                      (d) =>
                                        !columns.some(
                                          (c) => c.parentId === d.id
                                        )
                                    );

                                    // If no descendants, it's a leaf node itself
                                    const colSpanCount =
                                      descendants.length > 0
                                        ? leafNodes.length || 1
                                        : 1;

                                    return (
                                      <TableHead
                                        key={child.id}
                                        colSpan={colSpanCount}
                                        className="text-center border-b"
                                      >
                                        {child.columnName || "Child Column"}
                                      </TableHead>
                                    );
                                  });
                                });
                              })()}
                            </TableRow>
                          );
                        }

                        return headerRows;
                      })()}
                    </TableHeader>
                    <TableBody>
                      {rows.length > 0 ? (
                        rows.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {/* Render cells for each column starting from leftmost position */}
                            {columns
                              .filter((col) => {
                                // Show cells for top-level columns with no children
                                // or for child columns (level > 0)
                                const hasChildren = columns.some(
                                  (c) => c.parentId === col.id
                                );
                                return (
                                  (col.level === 0 && !hasChildren) ||
                                  col.level > 0
                                );
                              })
                              .map((col) => (
                                <TableCell
                                  key={col.id}
                                  className="bg-gray-50 p-0"
                                >
                                  {/* Extract the numeric ID from the column ID */}
                                  {(() => {
                                    const columnId = extractColumnId(col.id);
                                    return columnId ? (
                                      <Input
                                        type="text"
                                        placeholder="Enter value"
                                        className="border-0 h-8 text-center bg-transparent"
                                        value={getCellValue(rowIndex, col.id)}
                                        onChange={(e) =>
                                          handleCellValueChange(
                                            rowIndex,
                                            col.id,
                                            e.target.value
                                          )
                                        }
                                      />
                                    ) : (
                                      <div className="h-8 flex items-center justify-center text-gray-400 text-xs">
                                        Input field
                                      </div>
                                    );
                                  })()}
                                </TableCell>
                              ))}
                          </TableRow>
                        ))
                      ) : (
                        // When no rows exist, show a single row with input fields under columns
                        <TableRow>
                          {/* Render input cells for each column starting from leftmost position */}
                          {columns
                            .filter((col) => {
                              // Show cells for top-level columns with no children
                              // or for child columns (level > 0)
                              const hasChildren = columns.some(
                                (c) => c.parentId === col.id
                              );
                              return (
                                (col.level === 0 && !hasChildren) ||
                                col.level > 0
                              );
                            })
                            .map((col) => (
                              <TableCell key={col.id} className="bg-gray-50">
                                <div className="h-8 flex items-center justify-center text-gray-400 text-xs">
                                  Add rows to enter default values
                                </div>
                              </TableCell>
                            ))}
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  This preview shows how the table will appear to users filling
                  out the form.
                </p>
              </div>
            )}
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200 mb-4">
            <p className="font-medium mb-1">Table Structure Guidelines:</p>
            <ul className="list-disc pl-5 space-y-1">
              <li>
                Create multiple{" "}
                <span className="font-medium">parent columns</span> using the
                "Add Top-Level Column" button
              </li>
              <li>
                Add up to 2 <span className="font-medium">child columns</span>{" "}
                under each parent using the "+" button
              </li>
              <li>
                Child columns cannot have their own children (maximum 2 levels)
              </li>
            </ul>
          </div>

          <div>
            <Label htmlFor="table-label">Table Label</Label>
            <Input
              id="table-label"
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              placeholder="Enter table question label"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Columns</Label>
                {columns.map((column) => (
                  <div
                    key={column.id}
                    className={cn(
                      "flex items-center gap-2 p-2 rounded-md",
                      column.level === 0
                        ? "bg-gray-50"
                        : "bg-white border-l-2 border-gray-300"
                    )}
                    style={{ marginLeft: `${column.level * 20}px` }}
                  >
                    <div className="flex-1 flex items-center gap-2">
                      {column.level > 0 && (
                        <div className="w-4 h-4 flex items-center justify-center">
                          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                        </div>
                      )}
                      <Input
                        value={column.columnName}
                        onChange={(e) =>
                          handleColumnNameChange(column.id, e.target.value)
                        }
                        placeholder={`${
                          column.level === 0 ? "Parent" : "Child"
                        } Column`}
                        className={cn(
                          "flex-1",
                          column.level === 0
                            ? "border-blue-200 bg-white"
                            : "border-dashed"
                        )}
                      />
                      {column.level === 0 && (
                        <div className="text-xs text-blue-500 font-medium">
                          Parent
                        </div>
                      )}
                      {column.level > 0 && (
                        <div className="text-xs text-gray-500 font-medium">
                          Child
                        </div>
                      )}
                    </div>
                    <div className="flex items-center">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleAddChildColumn(column.id)}
                        title={
                          column.level > 0
                            ? "Child columns cannot have their own children"
                            : columns.filter(
                                (col) => col.parentId === column.id
                              ).length >= 2
                            ? "Maximum 2 child columns allowed"
                            : "Add child column"
                        }
                        disabled={
                          column.level > 0 || // Disable if this is already a child column
                          columns.filter((col) => col.parentId === column.id)
                            .length >= 2 // Disable if already has 2 children
                        }
                      >
                        <Plus
                          className={cn(
                            "h-4 w-4",
                            (column.level > 0 ||
                              columns.filter(
                                (col) => col.parentId === column.id
                              ).length >= 2) &&
                              "text-gray-300"
                          )}
                        />
                      </Button>

                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveColumn(column.id)}
                        disabled={columns.length <= 1}
                        title="Remove column"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddColumn}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Top-Level Column
                </Button>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">
                    Table Rows
                  </Label>
                  <NumericStepper
                    value={rows.length}
                    onChange={handleRowCountChange}
                    min={0}
                    max={1000}
                    hideInput={true}
                    className="flex-shrink-0"
                  />
                </div>
                <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded-md border border-blue-200">
                  <div className="flex items-center justify-between">
                    <span>
                      Rows will be automatically numbered (1, 2, 3...)
                    </span>
                    <span className="font-medium text-blue-700">
                      {rows.length} {rows.length === 1 ? "row" : "rows"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {showPreview && (
              <div className="space-y-2">
                <Label>Table Preview</Label>
                <div className="border rounded-md p-4 overflow-x-auto">
                  <Table>
                    <TableHeader>
                      {/* Calculate the maximum nesting level */}
                      {(() => {
                        const maxLevel = Math.max(
                          ...columns.map((col) => col.level),
                          0
                        );

                        // Create header rows for each level
                        const headerRows = [];

                        // First row with parent columns starting from leftmost position
                        headerRows.push(
                          <TableRow key="header-row-0">
                            {/* Render top-level (parent) columns starting from position 1 */}
                            {columns
                              .filter((col) => col.level === 0)
                              .map((parentCol) => {
                                // Count all descendants at all levels
                                const getAllDescendants = (
                                  colId: string
                                ): Column[] => {
                                  const directChildren = columns.filter(
                                    (c) => c.parentId === colId
                                  );
                                  let allDescendants = [...directChildren];

                                  directChildren.forEach((child) => {
                                    allDescendants = [
                                      ...allDescendants,
                                      ...getAllDescendants(child.id),
                                    ];
                                  });

                                  return allDescendants;
                                };

                                const descendants = getAllDescendants(
                                  parentCol.id
                                );
                                const leafNodes = descendants.filter(
                                  (d) =>
                                    !columns.some((c) => c.parentId === d.id)
                                );

                                // If no descendants, it's a leaf node itself
                                const colSpanCount =
                                  descendants.length > 0
                                    ? leafNodes.length || 1
                                    : 1;

                                return (
                                  <TableHead
                                    key={parentCol.id}
                                    colSpan={colSpanCount}
                                    className="text-center border-b"
                                  >
                                    {parentCol.columnName || "Column"}
                                  </TableHead>
                                );
                              })}
                          </TableRow>
                        );

                        // Create header rows for each additional level
                        for (let level = 1; level <= maxLevel; level++) {
                          headerRows.push(
                            <TableRow key={`header-row-${level}`}>
                              {/* For each level, we need to find columns at that level and their parents */}
                              {(() => {
                                const columnsAtLevel = columns.filter(
                                  (col) => col.level === level - 1
                                );

                                return columnsAtLevel.map((col) => {
                                  // Get direct children of this column
                                  const children = columns.filter(
                                    (c) => c.parentId === col.id
                                  );

                                  // If no children, render an empty cell to maintain alignment
                                  if (children.length === 0) {
                                    return (
                                      <TableHead
                                        key={`empty-${col.id}`}
                                        className="text-center border-b"
                                      >
                                        {/* Empty cell to maintain column alignment */}
                                      </TableHead>
                                    );
                                  }

                                  // Render each child with appropriate colspan
                                  return children.map((child) => {
                                    // Calculate how many leaf nodes are under this child
                                    const getAllDescendants = (
                                      colId: string
                                    ): Column[] => {
                                      const directChildren = columns.filter(
                                        (c) => c.parentId === colId
                                      );
                                      let allDescendants = [...directChildren];

                                      directChildren.forEach((child) => {
                                        allDescendants = [
                                          ...allDescendants,
                                          ...getAllDescendants(child.id),
                                        ];
                                      });

                                      return allDescendants;
                                    };

                                    const descendants = getAllDescendants(
                                      child.id
                                    );
                                    const leafNodes = descendants.filter(
                                      (d) =>
                                        !columns.some(
                                          (c) => c.parentId === d.id
                                        )
                                    );

                                    // If no descendants, it's a leaf node itself
                                    const colSpanCount =
                                      descendants.length > 0
                                        ? leafNodes.length || 1
                                        : 1;

                                    return (
                                      <TableHead
                                        key={child.id}
                                        colSpan={colSpanCount}
                                        className="text-center border-b"
                                      >
                                        {child.columnName || "Child Column"}
                                      </TableHead>
                                    );
                                  });
                                });
                              })()}
                            </TableRow>
                          );
                        }

                        return headerRows;
                      })()}
                    </TableHeader>
                    <TableBody>
                      {rows.length > 0 ? (
                        rows.map((row, rowIndex) => (
                          <TableRow key={rowIndex}>
                            {/* Render cells for each column starting from leftmost position */}
                            {columns
                              .filter((col) => {
                                // Show cells for top-level columns with no children
                                // or for child columns (level > 0)
                                const hasChildren = columns.some(
                                  (c) => c.parentId === col.id
                                );
                                return (
                                  (col.level === 0 && !hasChildren) ||
                                  col.level > 0
                                );
                              })
                              .map((col) => (
                                <TableCell
                                  key={col.id}
                                  className="bg-gray-50 p-0"
                                >
                                  {/* Extract the numeric ID from the column ID */}
                                  {(() => {
                                    const columnId = extractColumnId(col.id);
                                    return columnId ? (
                                      <Input
                                        type="text"
                                        placeholder="Enter value"
                                        className="border-0 h-8 text-center bg-transparent"
                                        value={getCellValue(rowIndex, col.id)}
                                        onChange={(e) =>
                                          handleCellValueChange(
                                            rowIndex,
                                            col.id,
                                            e.target.value
                                          )
                                        }
                                      />
                                    ) : (
                                      <div className="h-8 flex items-center justify-center text-gray-400 text-xs">
                                        Input field
                                      </div>
                                    );
                                  })()}
                                </TableCell>
                              ))}
                          </TableRow>
                        ))
                      ) : (
                        // When no rows exist, show a single row with input fields under columns
                        <TableRow>
                          {/* Render input cells for each column starting from leftmost position */}
                          {columns
                            .filter((col) => {
                              // Show cells for top-level columns with no children
                              // or for child columns (level > 0)
                              const hasChildren = columns.some(
                                (c) => c.parentId === col.id
                              );
                              return (
                                (col.level === 0 && !hasChildren) ||
                                col.level > 0
                              );
                            })
                            .map((col) => (
                              <TableCell key={col.id} className="bg-gray-50">
                                <div className="h-8 flex items-center justify-center text-gray-400 text-xs">
                                  Add rows to enter default values
                                </div>
                              </TableCell>
                            ))}
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  This preview shows how the table will appear to users filling
                  out the form.
                </p>
              </div>
            )}
          </div>

          <div className="flex items-center justify-end space-x-4 mt-6">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-primary-500 text-white hover:bg-primary-600"
            >
              {isSubmitting
                ? "Saving..."
                : isEditMode || existingTableData?.id
                ? "Update"
                : "Save"}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
