"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Minus } from "lucide-react";
import { cn } from "@/lib/utils";

export interface NumericStepperProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  disabled?: boolean;
  className?: string;
  label?: string;
  placeholder?: string;
}

export const NumericStepper = React.forwardRef<
  HTMLDivElement,
  NumericStepperProps
>(
  (
    {
      value,
      onChange,
      min = 1,
      max = 2147483647, // PostgreSQL INT4 max value
      step = 1,
      disabled = false,
      className,
      label,
      placeholder = "Enter number",
      ...props
    },
    ref
  ) => {
    const [inputValue, setInputValue] = useState(value.toString());
    const [isFocused, setIsFocused] = useState(false);

    // Update input value when prop value changes
    useEffect(() => {
      if (!isFocused) {
        setInputValue(value.toString());
      }
    }, [value, isFocused]);

    // Validate and clamp value within bounds
    const validateValue = useCallback(
      (newValue: number): number => {
        return Math.max(min, Math.min(max, newValue));
      },
      [min, max]
    );

    // Handle increment
    const handleIncrement = useCallback(() => {
      if (disabled) return;
      const newValue = validateValue(value + step);
      onChange(newValue);
    }, [value, step, disabled, validateValue, onChange]);

    // Handle decrement
    const handleDecrement = useCallback(() => {
      if (disabled) return;
      const newValue = validateValue(value - step);
      onChange(newValue);
    }, [value, step, disabled, validateValue, onChange]);

    // Handle input change
    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newInputValue = e.target.value;
        setInputValue(newInputValue);

        // Parse and validate the input
        const parsedValue = parseInt(newInputValue, 10);
        if (!isNaN(parsedValue)) {
          const validatedValue = validateValue(parsedValue);
          onChange(validatedValue);
        }
      },
      [validateValue, onChange]
    );

    // Handle input blur - ensure input shows valid value
    const handleInputBlur = useCallback(() => {
      setIsFocused(false);
      setInputValue(value.toString());
    }, [value]);

    // Handle input focus
    const handleInputFocus = useCallback(() => {
      setIsFocused(true);
    }, []);

    // Handle keyboard events
    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (disabled) return;

        switch (e.key) {
          case "ArrowUp":
            e.preventDefault();
            handleIncrement();
            break;
          case "ArrowDown":
            e.preventDefault();
            handleDecrement();
            break;
          case "Enter":
            e.preventDefault();
            (e.target as HTMLInputElement).blur();
            break;
        }
      },
      [disabled, handleIncrement, handleDecrement]
    );

    const canDecrement = !disabled && value > min;
    const canIncrement = !disabled && value < max;

    return (
      <div
        ref={ref}
        className={cn("flex items-center space-x-1", className)}
        {...props}
      >
        {label && (
          <span className="text-sm font-medium text-gray-700 mr-2">
            {label}:
          </span>
        )}
        
        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={handleDecrement}
          disabled={!canDecrement}
          className="h-8 w-8 shrink-0"
          aria-label="Decrease value"
        >
          <Minus className="h-3 w-3" />
        </Button>

        <Input
          type="number"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          min={min}
          max={max}
          step={step}
          placeholder={placeholder}
          className="h-8 w-16 text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
          aria-label={label || "Numeric value"}
        />

        <Button
          type="button"
          variant="outline"
          size="icon"
          onClick={handleIncrement}
          disabled={!canIncrement}
          className="h-8 w-8 shrink-0"
          aria-label="Increase value"
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
    );
  }
);

NumericStepper.displayName = "NumericStepper";
